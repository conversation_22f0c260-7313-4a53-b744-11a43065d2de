// Unit Recap Dashboard JavaScript

let problemComponentChart = null;
let selectedUnitId = null;

$(document).ready(function () {
    // Register Chart.js datalabels plugin
    if (typeof Chart !== 'undefined' && typeof ChartDataLabels !== 'undefined') {
        Chart.register(ChartDataLabels);
    }

    // Initialize the dashboard
    initializeDashboard();

    // Set up event handlers
    setupEventHandlers();

    // Load initial chart data
    loadProblemComponentChart();
});

function initializeDashboard() {
    // Set default date filters (current month)
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    $('#chart-start-date').val(firstDay.toISOString().split('T')[0]);
    $('#chart-end-date').val(lastDay.toISOString().split('T')[0]);
}

function setupEventHandlers() {
    // Chart date filters
    $('#chart-start-date, #chart-end-date').on('change', function() {
        loadProblemComponentChart();
    });

    // Unit search
    $('#unit-search').on('input', function() {
        const search = $(this).val();
        if (search.length >= 2) {
            searchUnits(search);
        } else {
            hideUnitDropdown();
        }
    });

    // Show all reports button
    $(document).on('click', '#btn-show-all-reports', function() {
        showAllUnitReports();
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#unit-search, #unit-dropdown').length) {
            hideUnitDropdown();
        }
    });
}

function loadProblemComponentChart() {
    $('#chart-loading-skeleton').removeClass('d-none');
    
    const startDate = $('#chart-start-date').val();
    const endDate = $('#chart-end-date').val();
    
    $.ajax({
        url: '/unit-recap/problem-component-data',
        type: 'GET',
        data: {
            start_date: startDate,
            end_date: endDate
        },
        success: function(response) {
            $('#chart-loading-skeleton').addClass('d-none');
            renderProblemComponentChart(response.data);
        },
        error: function(xhr) {
            $('#chart-loading-skeleton').addClass('d-none');
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load chart data'
                });
            } else {
                alert('Failed to load chart data');
            }
        }
    });
}

function renderProblemComponentChart(data) {
    const ctx = document.getElementById('problemComponentChart').getContext('2d');
    
    // Destroy existing chart if it exists
    if (problemComponentChart) {
        problemComponentChart.destroy();
    }
    
    const labels = data.map(item => item.problem_component);
    const counts = data.map(item => item.count);
    
    // Generate colors for bars
    const colors = generateColors(data.length);
    
    problemComponentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Number of Reports',
                data: counts,
                backgroundColor: colors.background,
                borderColor: colors.border,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'UNSCHEDULED Daily Reports by Problem Component',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.raw} reports`;
                        }
                    }
                },
                datalabels: {
                    display: true,
                    anchor: 'center',
                    align: 'center',
                    color: 'white',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    formatter: function(value) {
                        return value;
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    title: {
                        display: true,
                        text: 'Number of Reports'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Problem Component'
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            },
            onClick: function(event, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const problemComponent = labels[index];
                    showProblemComponentDetails(problemComponent);
                }
            },
            onHover: function(event, elements) {
                event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
            }
        }
    });
}

function generateColors(count) {
    const background = [];
    const border = [];
    
    for (let i = 0; i < count; i++) {
        const hue = (i * 360 / count) % 360;
        background.push(`hsla(${hue}, 70%, 60%, 0.8)`);
        border.push(`hsla(${hue}, 70%, 50%, 1)`);
    }
    
    return { background, border };
}

function showProblemComponentDetails(problemComponent) {
    $('#problem-component-modal-label').text(`Problem Component: ${problemComponent}`);
    $('#problem-component-modal').modal('show');
    $('#modal-loading').show();
    $('#modal-content').addClass('d-none');
    
    const startDate = $('#chart-start-date').val();
    const endDate = $('#chart-end-date').val();
    
    $.ajax({
        url: `/unit-recap/problem-component/${encodeURIComponent(problemComponent)}/details`,
        type: 'GET',
        data: {
            start_date: startDate,
            end_date: endDate
        },
        success: function(response) {
            $('#modal-loading').hide();
            $('#modal-content').removeClass('d-none');
            renderProblemComponentTable(response.data);
        },
        error: function(xhr) {
            $('#modal-loading').hide();
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load problem component details'
                });
            } else {
                alert('Failed to load problem component details');
            }
            $('#problem-component-modal').modal('hide');
        }
    });
}

function renderProblemComponentTable(data) {
    let html = '';
    
    if (data.length === 0) {
        html = `
            <tr>
                <td colspan="11" class="text-center py-4">
                    <i class="mdi mdi-file-document-outline" style="font-size: 48px; color: #ccc;"></i>
                    <p class="text-muted mt-2">No reports found</p>
                </td>
            </tr>
        `;
    } else {
        data.forEach(function(item) {
            // Create jobs display
            let jobsDisplay = '<ul class="mb-0 ps-3">';
            item.jobs.forEach(function(job) {
                if (job.highlight) {
                    jobsDisplay += `<li><span class="highlighted-job-text">- ${job.job_description}</span></li>`;
                } else {
                    jobsDisplay += `<li>- ${job.job_description}</li>`;
                }
            });
            jobsDisplay += '</ul>';
            
            // Create technicians display
            let techniciansList = '<ul class="mb-0 ps-3">';
            item.technicians.forEach(function(technician) {
                techniciansList += `<li>- ${technician}</li>`;
            });
            techniciansList += '</ul>';
            
            html += `
                <tr>
                    <td>${item.date_in}</td>
                    <td>
                        <strong>${item.unit_code}</strong><br>
                        <small class="text-muted">${item.unit_type}</small>
                    </td>
                    <td>${item.hm || '-'}</td>
                    <td>${item.problem || '-'}</td>
                    <td>${item.problem_description || '-'}</td>
                    <td>${jobsDisplay}</td>
                    <td>${item.hour_in}</td>
                    <td>${item.hour_out}</td>
                    <td>
                        <span class="badge ${item.shift === 'DAY' ? 'bg-warning' : 'bg-info'}">${item.shift}</span>
                    </td>
                    <td><strong>${item.dt_display}</strong></td>
                    <td>${techniciansList}</td>
                </tr>
            `;
        });
    }
    
    $('#modal-table-body').html(html);
}

function searchUnits(search) {
    $.ajax({
        url: '/unit-recap/units/search',
        type: 'GET',
        data: { search: search },
        success: function(response) {
            renderUnitDropdown(response.data);
        },
        error: function(xhr) {
            hideUnitDropdown();
        }
    });
}

function renderUnitDropdown(units) {
    let html = '';
    
    if (units.length === 0) {
        html = '<div class="dropdown-item text-muted">No units found</div>';
    } else {
        units.forEach(function(unit) {
            html += `
                <div class="dropdown-item" onclick="selectUnit(${unit.id}, '${unit.unit_code}', '${unit.unit_type}')">
                    <strong>${unit.unit_code}</strong><br>
                    <small class="text-muted">${unit.unit_type}</small>
                </div>
            `;
        });
    }
    
    $('#unit-dropdown').html(html).show();
}

function hideUnitDropdown() {
    $('#unit-dropdown').hide();
}

function selectUnit(unitId, unitCode, unitType) {

    
    selectedUnitId = unitId;
    $('#unit-search').val(`${unitCode} - ${unitType}`);
    hideUnitDropdown();
    
    // I DONT WANNA SHOW THIS FOR A WHILE
    // Show selected unit info
    // $('#selected-unit-details').html(`<strong>${unitCode}</strong><br><small>${unitType}</small>`);
    // $('#selected-unit-info').removeClass('d-none');

    // Load service prediction for this unit
    loadServicePrediction(unitId);

    // Load open backlogs for this unit first
    loadUnitBacklogs(unitId);

    // Load report dates for this unit
    loadUnitReportDates(unitId);
}

function loadUnitBacklogs(unitId) {
    $.ajax({
        url: `/unit-recap/units/${unitId}/backlogs`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                renderUnitBacklogs(response.data);
                $('#unit-backlogs-section').removeClass('d-none');
            } else {
                $('#unit-backlogs-section').addClass('d-none');
            }
        },
        error: function(xhr) {
            $('#unit-backlogs-section').addClass('d-none');
            console.error('Failed to load unit backlogs:', xhr);
        }
    });
}

function renderUnitBacklogs(backlogs) {
    let html = '';

    backlogs.forEach(function(backlog) {
        html += `
            <div class="card border-danger mb-2">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title text-danger mb-1">
                                <i class="mdi mdi-wrench"></i> ${backlog.backlog_job}
                            </h6>
                            <p class="card-text mb-1">
                                <strong>Problem:</strong> ${backlog.problem_description}
                            </p>
                            <small class="text-muted">
                                <i class="mdi mdi-calendar"></i> Created: ${backlog.created_date}
                                ${backlog.hm_found ? ` <br> <i class="mdi mdi-speedometer"></i> HM Found: ${backlog.hm_found}` : ''}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-danger mb-1">${backlog.status}</span><br>
                            ${backlog.plan_pull_date !== '-' ? `<small class="text-muted"><i class="mdi mdi-clock"></i> Plan: ${backlog.plan_pull_date}</small>` : ''}
                            ${backlog.plan_hm ? `<br><small class="text-muted"><i class="mdi mdi-speedometer"></i> Plan HM: ${backlog.plan_hm}</small>` : ''}
                        </div>
                    </div>
                    ${backlog.notes ? `<div class="mt-2"><small class="text-muted"><strong>Notes:</strong> ${backlog.notes}</small></div>` : ''}
                </div>
            </div>
        `;
    });

    $('#unit-backlogs-list').html(html);
}

function loadServicePrediction(unitId) {
    $('#service-prediction-section').removeClass('d-none');
    $('#prediction-loading').removeClass('d-none');
    $('#prediction-content').addClass('d-none');
    $('#prediction-no-data').addClass('d-none');

    $.ajax({
        url: `/service-prediction/unit/${unitId}`,
        type: 'GET',
        success: function(response) {
            $('#prediction-loading').addClass('d-none');

            if (response.success) {
                renderServicePrediction(response.data);
                $('#prediction-content').removeClass('d-none');
            } else {
                $('#prediction-no-data').removeClass('d-none');
            }
        },
        error: function(xhr) {
            $('#prediction-loading').addClass('d-none');
            $('#prediction-no-data').removeClass('d-none');
            console.error('Failed to load service prediction:', xhr);
        }
    });
}

function renderServicePrediction(data) {
    $('#current-hm').text(data.current_hm || '-');
    $('#last-service-date').text(data.last_service_date || '-');
    $('#next-service-hm').text(data.next_service_hm || '-');
    $('#remaining-hm').text(data.remaining_hm || '-');
    $('#predicted-service-date').text(data.predicted_service_date_formatted || '-');
    $('#days-until-service').text(data.days_until_service || '-');
}

function loadUnitReportDates(unitId) {
    $('#dates-loading').removeClass('d-none');
    $('#report-dates-container').addClass('d-none');
    $('#no-dates-message').addClass('d-none');
    
    $.ajax({
        url: `/unit-recap/units/${unitId}/dates`,
        type: 'GET',
        success: function(response) {
            $('#dates-loading').addClass('d-none');
            
            if (response.data.length === 0) {
                $('#no-dates-message').removeClass('d-none');
            } else {
                renderReportDates(response.data);
                $('#report-dates-container').removeClass('d-none');
            }
        },
        error: function(xhr) {
            $('#dates-loading').addClass('d-none');
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load report dates'
                });
            } else {
                alert('Failed to load report dates');
            }
        }
    });
}

function renderReportDates(dates) {
    let html = '';
    
    dates.forEach(function(date) {
        html += `
            <a href="#" class="list-group-item list-group-item-action pt-1 pb-1 pl-2 pr-4" onclick="showUnitDateReports('${date.date_raw}', '${date.date_formatted}')">
                ${date.date_formatted}
            </a>
        `;
    });
    
    $('#report-dates-list').html(html);
}

function showUnitDateReports(dateRaw, dateFormatted) {
    if (!selectedUnitId) return;
    
    $('#unit-date-modal-label').text(`Unit Reports - ${dateFormatted}`);
    $('#unit-date-modal').modal('show');
    $('#unit-modal-loading').show();
    $('#unit-modal-content').addClass('d-none');
    
    $.ajax({
        url: `/unit-recap/units/${selectedUnitId}/dates/${dateRaw}/reports`,
        type: 'GET',
        success: function(response) {
            $('#unit-modal-loading').hide();
            $('#unit-modal-content').removeClass('d-none');
            renderUnitDateTable(response.data);
        },
        error: function(xhr) {
            $('#unit-modal-loading').hide();
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load unit reports'
                });
            } else {
                alert('Failed to load unit reports');
            }
            $('#unit-date-modal').modal('hide');
        }
    });
}

function renderUnitDateTable(data) {
    let html = '';
    
    if (data.length === 0) {
        html = `
            <tr>
                <td colspan="12" class="text-center py-4">
                    <i class="mdi mdi-file-document-outline" style="font-size: 48px; color: #ccc;"></i>
                    <p class="text-muted mt-2">No reports found</p>
                </td>
            </tr>
        `;
    } else {
        data.forEach(function(item) {
            // Create jobs display
            let jobsDisplay = '<ul class="mb-0 ps-3">';
            item.jobs.forEach(function(job) {
                if (job.highlight) {
                    jobsDisplay += `<li><span class="highlighted-job-text">- ${job.job_description}</span></li>`;
                } else {
                    jobsDisplay += `<li>- ${job.job_description}</li>`;
                }
            });
            jobsDisplay += '</ul>';
            
            // Create technicians display
            let techniciansList = '<ul class="mb-0 ps-3">';
            item.technicians.forEach(function(technician) {
                techniciansList += `<li>- ${technician}</li>`;
            });
            techniciansList += '</ul>';
            
            html += `
                <tr>
                    <td>${item.date_in}</td>
                    <td>
                        <strong>${item.unit_code}</strong><br>
                        <small class="text-muted">${item.unit_type}</small>
                    </td>
                    <td>${item.hm || '-'}</td>
                    <td>${item.problem || '-'}</td>
                    <td>${item.problem_component || '-'}</td>
                    <td>${item.problem_description || '-'}</td>
                    <td>${jobsDisplay}</td>
                    <td>${item.hour_in}</td>
                    <td>${item.hour_out}</td>
                    <td>
                        <span class="badge ${item.shift === 'DAY' ? 'bg-warning' : 'bg-info'}">${item.shift}</span>
                    </td>
                    <td><strong>${item.dt_display}</strong></td>
                    <td>${techniciansList}</td>
                </tr>
            `;
        });
    }
    
    $('#unit-modal-table-body').html(html);
}

// Show all reports for selected unit
function showAllUnitReports() {
    if (!selectedUnitId) return;

    const startDate = $('#chart-start-date').val();
    const endDate = $('#chart-end-date').val();

    $('#unit-date-modal-label').text(`All Unit Reports - Unit ID: ${selectedUnitId}`);
    $('#unit-date-modal').modal('show');
    $('#unit-modal-loading').show();
    $('#unit-modal-content').addClass('d-none');

    $.ajax({
        url: `/unit-recap/units/${selectedUnitId}/all-reports`,
        type: 'GET',
        data: {
            start_date: startDate,
            end_date: endDate
        },
        success: function(response) {
            $('#unit-modal-loading').hide();
            $('#unit-modal-content').removeClass('d-none');
            renderUnitDateTable(response.data);
        },
        error: function() {
            $('#unit-modal-loading').hide();
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load all unit reports'
                });
            } else {
                alert('Failed to load all unit reports');
            }
            $('#unit-date-modal').modal('hide');
        }
    });
}

// Make functions globally accessible
window.selectUnit = selectUnit;
window.showUnitDateReports = showUnitDateReports;
window.showAllUnitReports = showAllUnitReports;
