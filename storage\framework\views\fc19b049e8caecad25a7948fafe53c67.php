<?php $__env->startSection('title', 'Edit Backlog'); ?>
<?php $__env->startSection('resourcesite'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('backlogs.index')); ?>">Backlog Management</a></li>
                        <li class="breadcrumb-item active">Edit Backlog</li>
                    </ol>
                </div>
                <h4 class="page-title">Edit Backlog</h4>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-4">Edit Backlog Information</h4>
                    
                    <form id="edit-backlog-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unit_code" class="form-label">Unit Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="unit_code" name="unit_code" value="<?php echo e($backlog->unit_code); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hm_found" class="form-label">HM Found</label>
                                    <input type="number" class="form-control" id="hm_found" name="hm_found" value="<?php echo e($backlog->hm_found); ?>" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="problem_description" class="form-label">Problem Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="problem_description" name="problem_description" value="<?php echo e($backlog->problem_description); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="backlog_job" class="form-label">Backlog Job <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="backlog_job" name="backlog_job" value="<?php echo e($backlog->backlog_job); ?>" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="plan_hm" class="form-label">Plan HM</label>
                                    <input type="number" class="form-control" id="plan_hm" name="plan_hm" value="<?php echo e($backlog->plan_hm); ?>" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="OPEN" <?php echo e($backlog->status == 'OPEN' ? 'selected' : ''); ?>>Open</option>
                                        <option value="CLOSED" <?php echo e($backlog->status == 'CLOSED' ? 'selected' : ''); ?>>Closed</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="plan_pull_date" class="form-label">Plan Pull Date</label>
                            <input type="datetime-local" class="form-control" id="plan_pull_date" name="plan_pull_date" 
                                   value="<?php echo e($backlog->plan_pull_date ? $backlog->plan_pull_date->format('Y-m-d\TH:i') : ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="4"><?php echo e($backlog->notes); ?></textarea>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo e(route('backlogs.index')); ?>" class="btn btn-secondary me-md-2">
                                <i class="mdi mdi-arrow-left"></i> Back
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-content-save"></i> Update Backlog
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#edit-backlog-form').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: '<?php echo e(route("backlogs.update", $backlog->id)); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        window.location.href = '<?php echo e(route("backlogs.index")); ?>';
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = '';
                    Object.keys(errors).forEach(key => {
                        errorMessage += errors[key][0] + '\n';
                    });
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Error',
                        text: errorMessage
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat memperbarui data'
                    });
                }
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/backlogs/edit.blade.php ENDPATH**/ ?>