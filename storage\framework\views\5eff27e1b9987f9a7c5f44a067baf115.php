<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <title>Pilih Mode Akses | Portal PWB</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/login.css']); ?>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: url('<?php echo e(asset('assets/images/438463.png')); ?>');
            background-size: cover;
            background-position: center;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .mode-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }

        .mode-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 300px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .mode-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .mode-icon {
            font-size: 60px;
            margin-bottom: 20px;
            color: #4a90e2;
        }

        .mode-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .mode-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .mode-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }

        .mode-button:hover {
            background-color: #3a7bc8;
        }

        .logout-link {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            text-decoration: none;
            font-weight: bold;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 8px 15px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .logout-link:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .welcome-text {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-weight: bold;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 8px 15px;
            border-radius: 5px;
        }
    </style>
</head>

<body>
    <div class="welcome-text">
        Selamat Datang, <?php echo e(session('name')); ?> (<?php echo e(session('site_id')); ?>)
    </div>

    <a href="<?php echo e(route('logout')); ?>" class="logout-link">
        <i class="fas fa-sign-out-alt"></i> Logout
    </a>

    <div class="mode-container">
        <form id="mode-form" action="<?php echo e(route('adminsite.mode.select.post')); ?>" method="POST" style="display: none;">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="mode" id="selected-mode">
        </form>

        <div class="mode-card" onclick="selectMode('inventory')">
            <div class="mode-icon">
                <i class="fas fa-box-open"></i>
            </div>
            <div class="mode-title">Inventori</div>
            <div class="mode-description">
                Akses ke semua fitur inventori, manajemen unit, dan transaksi unit.
            </div>
            <button class="mode-button">Pilih Mode Inventori</button>
        </div>

        <div class="mode-card" onclick="selectMode('daily_report')">
            <div class="mode-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="mode-title">Daily Report</div>
            <div class="mode-description">
                Halaman Pembuatan Daily Report untuk unit dan teknisi
            </div>
            <button class="mode-button">Pilih Mode Daily Report</button>
        </div>
    </div>

    <script>
        function selectMode(mode) {
            document.getElementById('selected-mode').value = mode;
            document.getElementById('mode-form').submit();
        }
    </script>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/auth/mode-select.blade.php ENDPATH**/ ?>